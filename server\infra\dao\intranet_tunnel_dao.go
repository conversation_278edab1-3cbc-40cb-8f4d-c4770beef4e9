package dao

import (
	"sync"
	"time"

	repo "socks/server/infra/repo"
	util "socks/server/util"

	"gorm.io/gorm"
)

var (
	dao  *IntranetTunnelDaoImpl
	once sync.Once
)

type IntranetTunnelDaoImpl struct {
	db gorm.DB
}

func GetIntranetTunnelDaoImpl() *IntranetTunnelDaoImpl {
	once.Do(func() {
		dao = &IntranetTunnelDaoImpl{
			db: *util.GetDB(),
		}
		// dao.db.AutoMigrate(&repo.IntranetTunnel{})
	})

	return dao
}

// Create 创建新的隧道记录
func (it *IntranetTunnelDaoImpl) Create(tunnel *repo.IntranetTunnel) error {
	var existingTunnel repo.IntranetTunnel
	err := it.db.Where("name = ?", tunnel.Name).First(&existingTunnel).Error
	if err == nil {
		tunnel.ID = existingTunnel.ID
	}
	return it.db.Save(tunnel).Error
}

// GetByID 根据ID获取隧道
func (it *IntranetTunnelDaoImpl) GetIntranetTunnelByID(id uint) (*repo.IntranetTunnel, error) {
	var tunnel repo.IntranetTunnel
	err := it.db.First(&tunnel, id).Error
	if err != nil {
		return nil, err
	}
	return &tunnel, nil
}

// GetAll 获取所有隧道
func (it *IntranetTunnelDaoImpl) GetAll() ([]repo.IntranetTunnel, error) {
	var tunnels []repo.IntranetTunnel
	err := it.db.Find(&tunnels).Error
	return tunnels, err
}

// GetEnabled 获取所有启用的隧道
func (it *IntranetTunnelDaoImpl) GetEnabled() ([]repo.IntranetTunnel, error) {
	var tunnels []repo.IntranetTunnel
	err := it.db.Where("enable = ?", true).Find(&tunnels).Error
	return tunnels, err
}

// Update 更新隧道信息
func (it *IntranetTunnelDaoImpl) Update(tunnel *repo.IntranetTunnel) error {
	return it.db.Save(tunnel).Error
}

// Delete 删除隧道
func (it *IntranetTunnelDaoImpl) Delete(id uint) error {
	return it.db.Delete(&repo.IntranetTunnel{}, id).Error
}

// UpdateLastConnectionTime 更新最后连接时间
func (it *IntranetTunnelDaoImpl) UpdateLastConnectionTime(id uint) error {
	return it.db.Model(&repo.IntranetTunnel{}).Where("id = ?", id).
		Update("lastconnectiontime", time.Now()).Error
}

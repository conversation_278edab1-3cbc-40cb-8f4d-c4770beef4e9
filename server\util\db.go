package util

import (
	"errors"
	"fmt"
	"log"
	"strings"
	"sync"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db     *gorm.DB
	once   sync.Once
	config = GetSystemConfig("")
)

type AutoIncrement struct {
	Id int
	m  sync.Mutex
}

func GetDB() *gorm.DB {
	var err error
	once.Do(func() {
		db, err = ConnectGormDB(config)
		if err != nil {
			log.Println(err)
		}
	})
	return db
}

// ConnectGormDB 使用GORM连接数据库
func ConnectGormDB(config *TunnelConfig) (*gorm.DB, error) {
	if config.DbType == "" || config.DbConnectCommand == "" {
		return nil, errors.New("数据库类型或连接字符串为空")
	}

	var db *gorm.DB
	var err error

	switch config.DbType {
	case "pgsql":
		// 将分号分隔的连接字符串转换为PostgreSQL标准格式
		connStr := convertPgConnStr(config.DbConnectCommand)
		db, err = gorm.Open(postgres.Open(connStr), &gorm.Config{})
	case "mysql":
		db, err = gorm.Open(mysql.Open(config.DbConnectCommand), &gorm.Config{})
	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", config.DbType)
	}

	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取底层数据库连接失败: %v", err)
	}

	if err = sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %v", err)
	}

	return db, nil
}

// convertPgConnStr 将分号分隔的连接字符串转换为PostgreSQL标准格式
func convertPgConnStr(connStr string) string {
	// 将分号分隔的键值对转换为空格分隔的键值对
	// 例如: "host=BaseOSConfigDB;port=5432" -> "host=BaseOSConfigDB port=5432"
	result := ""
	pairs := strings.Split(connStr, ";")

	for i, pair := range pairs {
		if i > 0 {
			result += " "
		}
		// 处理特殊情况，如Username需要转换为user
		if strings.HasPrefix(strings.ToLower(pair), "username=") {
			parts := strings.SplitN(pair, "=", 2)
			if len(parts) == 2 {
				result += "user=" + parts[1]
			}
		} else if strings.HasPrefix(strings.ToLower(pair), "searchpath=") {
			// 处理SearchPath
			parts := strings.SplitN(pair, "=", 2)
			if len(parts) == 2 {
				result += "search_path=" + parts[1]
			}
		} else {
			result += pair
		}
	}
	log.Println(result)
	return result
}

func (auto *AutoIncrement) GetId() int {
	auto.m.Lock()
	defer auto.m.Unlock()
	auto.Id++
	return auto.Id
}

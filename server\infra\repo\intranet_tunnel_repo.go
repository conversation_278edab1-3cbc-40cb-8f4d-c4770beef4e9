package repo

import "time"

type IntranetTunnel struct {
	ID                 int       `gorm:"primaryKey;autoIncrement:true;column:id"`
	UUID               string    `gorm:"size:100;column:clientid"`
	Name               string    `gorm:"size:100;not null;column:name"`
	ClientName         string    `gorm:"size:100;column:clientname"`
	ClientIp           string    `gorm:"size:100;column:clientip"`
	Protocol           string    `gorm:"size:20;column:protocol"`
	ServerPort         int       `gorm:"column:serverport"`
	ClientPort         int       `gorm:"column:clientport"`
	Enable             bool      `gorm:"column:enable"`
	Description        string    `gorm:"size:500;column:description"`
	Encryption         bool      `gorm:"column:encryption"`
	Password           string    `gorm:"size:100;column:password"`
	RateLimit          int       `gorm:"column:ratelimit"`
	CreateTime         time.Time `gorm:"column:createtime"`
	LastConnectionTime time.Time `gorm:"column:lastconnectiontime"`
}

// TableName 指定表名
func (IntranetTunnel) TableName() string {
	return "gateway_intranet_tunnel"
}

package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"math/rand"
	"net"
	"net/http"
	dao "socks/server/infra/dao"
	repo "socks/server/infra/repo"
	util "socks/server/util"
	"strconv"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

var (
	itDao = dao.GetIntranetTunnelDaoImpl()
	// 获取系统配置
	sysConfig = util.GetSystemConfig()
)

// Message 定义了 WS 上传输的消息格式，用于多路复用
type Message struct {
	ID   string `json:"id"`             // 连接 ID
	Type string `json:"type"`           // open, data, close
	Data []byte `json:"data,omitempty"` // 二进制数据
}

// SafeConn 包装 websocket.Conn，保证并发写安全
type SafeConn struct {
	*websocket.Conn
	wmu sync.Mutex
}

// PortMapping 存储端口映射信息
type PortMapping struct {
	PC         int          // 本地端口
	PS         int          // 公网端口
	ClientName string       // 客户端名称
	ClientIP   string       // 客户端IP地址
	CientUUID  string       // 客户端唯一id
	Listener   net.Listener // 监听器
	Created    time.Time    // 创建时间
}

func (c *SafeConn) WriteJSON(v interface{}) error {
	c.wmu.Lock()
	defer c.wmu.Unlock()
	return c.Conn.WriteJSON(v)
}

var (
	upgrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool { return true },
	}

	// 持有每个 ps 对应的 WS 连接
	wsConns    = make(map[int]*SafeConn)
	wsConnsMux sync.Mutex

	// 存放每个连接 ID 对应的响应通道
	respChans = make(map[string]chan []byte)
	respMu    sync.Mutex

	// 全局端口映射列表
	portMappings    = make(map[int]*PortMapping) // 公网端口 -> 映射信息
	portMappingsMux sync.Mutex

	// 已分配的端口集合
	allocatedPorts    = make(map[int]bool) // 公网端口 -> 是否已分配
	allocatedPortsMux sync.Mutex

	// 客户端映射缓存 (clientUUID+localPort -> 服务端端口)
	clientPortCache    = make(map[string]int)
	clientPortCacheMux sync.Mutex

	// 缓存过期时间(默认30分钟)
	portCacheExpiration = 30 * time.Minute
)

// 分配一个未被占用的随机端口
func allocateRandomPort() (int, error) {
	// 最多尝试20次
	for i := 0; i < 20; i++ {
		allocatedPortsMux.Lock()
		// 使用配置的端口范围生成随机端口
		portRange := sysConfig.MaxPort - sysConfig.MinPort
		ps := rand.Intn(portRange) + sysConfig.MinPort

		// 检查端口是否已被分配
		if allocatedPorts[ps] {
			allocatedPortsMux.Unlock()
			continue
		}

		// 尝试监听该端口，检查是否可用
		ln, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", ps))
		if err != nil {
			allocatedPortsMux.Unlock()
			log.Printf("端口 %d 不可用: %v", ps, err)
			continue
		}
		defer ln.Close()

		// 标记端口为已分配
		allocatedPorts[ps] = true
		allocatedPortsMux.Unlock()

		return ps, nil
	}

	return 0, fmt.Errorf("无法分配可用端口，请稍后再试")
}

// 释放端口
func releasePort(ps int) {
	portMappingsMux.Lock()
	if mapping, exists := portMappings[ps]; exists {
		if mapping.Listener != nil {
			mapping.Listener.Close()
		}
		delete(portMappings, ps)
	}
	portMappingsMux.Unlock()

	allocatedPortsMux.Lock()
	delete(allocatedPorts, ps)
	allocatedPortsMux.Unlock()

	log.Printf("端口 %d 已释放", ps)
}

// 从数据库加载隧道记录，初始化缓存和自增ID
func loadTunnelRecordsFromDB() {
	// 获取所有隧道记录
	tunnels, err := itDao.GetAll()
	if err != nil {
		log.Printf("加载隧道记录失败: %v", err)
		return
	}

	log.Printf("从数据库加载了 %d 条隧道记录", len(tunnels))

	// 处理每条记录
	clientPortCacheMux.Lock()
	allocatedPortsMux.Lock()
	defer clientPortCacheMux.Unlock()
	defer allocatedPortsMux.Unlock()

	for _, tunnel := range tunnels {

		// 构建缓存键
		cacheKey := fmt.Sprintf("%s:%d", tunnel.UUID, tunnel.ClientPort)

		// 添加到缓存
		clientPortCache[cacheKey] = tunnel.ServerPort

		// 标记服务端口为已分配
		allocatedPorts[tunnel.ServerPort] = true

		log.Printf("缓存记录: UUID=%s, 本地端口=%d, 服务端口=%d",
			tunnel.UUID, tunnel.ClientPort, tunnel.ServerPort)
	}

}

// 尝试恢复启用的端口映射
func restoreEnabledPortMappings() {
	// 获取所有启用的隧道记录
	tunnels, err := itDao.GetEnabled()
	if err != nil {
		log.Printf("加载启用的隧道记录失败: %v", err)
		return
	}

	log.Printf("尝试恢复 %d 个启用的端口映射", len(tunnels))

	for _, tunnel := range tunnels {
		// 尝试监听该端口
		ln, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", tunnel.ServerPort))
		if err != nil {
			log.Printf("无法恢复端口 %d: %v", tunnel.ServerPort, err)
			continue
		}

		// 创建映射信息
		portMapping := &PortMapping{
			PC:         tunnel.ClientPort,
			PS:         tunnel.ServerPort,
			ClientName: tunnel.ClientName,
			ClientIP:   tunnel.ClientIp,
			CientUUID:  tunnel.UUID,
			Listener:   ln,
			Created:    tunnel.CreateTime,
		}

		// 记录映射信息
		portMappingsMux.Lock()
		portMappings[tunnel.ServerPort] = portMapping
		portMappingsMux.Unlock()

		log.Printf("恢复端口映射: 本地端口=%d, 服务端口=%d, UUID=%s",
			tunnel.ClientPort, tunnel.ServerPort, tunnel.UUID)

		// 启动监听
		go handleListener(tunnel.ServerPort, ln)
	}
}

func main() {
	// 使用配置的管理端口，但允许通过命令行参数覆盖
	defaultPort := strconv.Itoa(sysConfig.ManagerPort)
	port := flag.String("port", defaultPort, "监听端口")

	// 使用配置的过期时间（天转换为分钟）
	defaultCacheExpiration := sysConfig.SlidingExpiration * 24 * 60 // 天转换为分钟
	cacheExpiration := flag.Int("cache", defaultCacheExpiration, "端口缓存过期时间(分钟)")
	flag.Parse()

	// 设置缓存过期时间
	if *cacheExpiration > 0 {
		portCacheExpiration = time.Duration(*cacheExpiration) * time.Minute
	}

	// 初始化随机数生成器
	rand.New(rand.NewSource(time.Now().UnixNano()))

	// 从数据库加载隧道记录，初始化缓存和自增ID
	loadTunnelRecordsFromDB()

	// 尝试恢复启用的端口映射
	restoreEnabledPortMappings()

	// 启动缓存清理协程
	go cleanExpiredCache(portCacheExpiration)

	http.HandleFunc("/allocate", allocateHandler)
	http.HandleFunc("/ws", wsHandler)
	http.HandleFunc("/status", statusHandler)
	http.HandleFunc("/tunnel/refresh", statusHandler)

	log.Printf("服务器配置: 端口范围 %d-%d, 最大连接数 %d, 连接超时 %d秒",
		sysConfig.MinPort, sysConfig.MaxPort, sysConfig.MaxConnection, sysConfig.Timeout)
	log.Printf("服务器监听端口: %s, 端口缓存过期时间: %v", *port, portCacheExpiration)
	log.Fatal(http.ListenAndServe(":"+*port, nil))
}

// 显示当前端口映射状态
func statusHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	type MappingInfo struct {
		PC       int       `json:"pc"`
		PS       int       `json:"ps"`
		ClientIP string    `json:"client_ip"`
		Created  time.Time `json:"created"`
		Active   bool      `json:"active"`
	}

	var mappings []MappingInfo

	portMappingsMux.Lock()
	wsConnsMux.Lock()

	for ps, mapping := range portMappings {
		_, active := wsConns[ps]
		mappings = append(mappings, MappingInfo{
			PC:       mapping.PC,
			PS:       mapping.PS,
			ClientIP: mapping.ClientIP,
			Created:  mapping.Created,
			Active:   active,
		})
	}

	wsConnsMux.Unlock()
	portMappingsMux.Unlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"mappings": mappings,
		"count":    len(mappings),
	})
}

// 分配一个公网端口，并为该 pc 启动监听
func allocateHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取参数
	pcStr := r.URL.Query().Get("pc")
	if pcStr == "" {
		http.Error(w, "missing pc", http.StatusBadRequest)
		return
	}
	pc, err := strconv.Atoi(pcStr)
	if err != nil {
		http.Error(w, "invalid pc", http.StatusBadRequest)
		return
	}

	// 获取客户端上报的IP地址
	clientIP := r.URL.Query().Get("client_ip")
	if clientIP == "" {
		// 如果客户端没有上报IP，使用备用方法
		clientIP = r.RemoteAddr
		// 如果有代理，尝试获取真实IP
		if forwardedFor := r.Header.Get("X-Forwarded-For"); forwardedFor != "" {
			clientIP = forwardedFor
		}
		// 去除端口部分
		if host, _, err := net.SplitHostPort(clientIP); err == nil {
			clientIP = host
		}
	}

	clientUUID := r.URL.Query().Get("id")
	if clientUUID == "" {
		http.Error(w, "invalid id", http.StatusBadRequest)
		return
	}

	clientName := r.URL.Query().Get("name")

	// 检查缓存中是否有相同UUID+本地端口的映射
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, pc)
	clientPortCacheMux.Lock()
	cachedPS, exists := clientPortCache[cacheKey]
	clientPortCacheMux.Unlock()

	var ps int

	if exists {
		// 检查缓存的端口是否仍然可用
		portMappingsMux.Lock()
		mapping, portExists := portMappings[cachedPS]
		portMappingsMux.Unlock()

		if portExists && mapping.CientUUID == clientUUID && mapping.PC == pc {
			// 如果端口仍然存在且映射信息匹配，复用该端口
			ps = cachedPS
			log.Printf("复用缓存端口 ps=%d 给 pc=%d (客户端UUID: %s)", ps, pc, clientUUID)

			// 更新响应
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]int{"ps": ps})
			return
		}
	}

	// 分配随机端口
	ps, err = allocateRandomPort()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 创建监听器
	ln, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", ps))
	if err != nil {
		// 释放端口并返回错误
		releasePort(ps)
		http.Error(w, "listen error", http.StatusInternalServerError)
		return
	}

	// 记录映射信息
	portMappingsMux.Lock()
	portMappings[ps] = &PortMapping{
		PC:         pc,
		PS:         ps,
		ClientName: clientName,
		ClientIP:   clientIP,
		CientUUID:  clientUUID,
		Listener:   ln,
		Created:    time.Now(),
	}
	portMappingsMux.Unlock()

	// 更新缓存
	clientPortCacheMux.Lock()
	clientPortCache[cacheKey] = ps
	clientPortCacheMux.Unlock()

	log.Printf("分配端口 ps=%d 给 pc=%d (客户端UUID: %s, IP: %s)", ps, pc, clientUUID, clientIP)
	go handleListener(ps, ln)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]int{"ps": ps})
}

// 处理 WS 握手并保存 SafeConn
func wsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet || !websocket.IsWebSocketUpgrade(r) {
		http.Error(w, "Invalid WS request", http.StatusBadRequest)
		return
	}
	psStr := r.URL.Query().Get("ps")
	ps, err := strconv.Atoi(psStr)
	if err != nil {
		http.Error(w, "invalid ps", http.StatusBadRequest)
		return
	}

	// 获取客户端IP地址
	clientIP := r.RemoteAddr
	if forwardedFor := r.Header.Get("X-Forwarded-For"); forwardedFor != "" {
		clientIP = forwardedFor
	}
	if host, _, err := net.SplitHostPort(clientIP); err == nil {
		clientIP = host
	}

	// 检查端口是否已分配
	portMappingsMux.Lock()
	_, exists := portMappings[ps]
	portMappingsMux.Unlock()

	if !exists {
		http.Error(w, "port not allocated", http.StatusBadRequest)
		return
	}

	// 检查是否超过最大连接数
	wsConnsMux.Lock()
	if len(wsConns) >= sysConfig.MaxConnection {
		wsConnsMux.Unlock()
		http.Error(w, "服务器连接数已达上限", http.StatusServiceUnavailable)
		return
	}
	wsConnsMux.Unlock()

	// 可选：验证客户端IP地址是否匹配（如果需要严格安全性）
	// 注释掉这段代码可以允许不同IP的客户端连接
	/*
		if mapping.ClientIP != clientIP {
			log.Printf("IP不匹配: 预期 %s, 实际 %s", mapping.ClientIP, clientIP)
			http.Error(w, "unauthorized client", http.StatusUnauthorized)
			return
		}
	*/

	ws, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("Upgrade error: %v", err)
		return
	}
	sconn := &SafeConn{Conn: ws}

	wsConnsMux.Lock()
	wsConns[ps] = sconn
	wsConnsMux.Unlock()

	if portInfo, ok := portMappings[ps]; ok {
		go func() {
			err := itDao.Create(portInfo.ToRepo())
			if err != nil {
				log.Printf("create conn info error: %v", err)
			}
		}()
	}

	log.Printf("WS connected for ps=%d (客户端IP: %s)", ps, clientIP)

	go handleWS(sconn, ps)
}

// 监听公网端口，接受新连接并转发
func handleListener(ps int, ln net.Listener) {
	defer func() {
		ln.Close()
		releasePort(ps)
	}()

	log.Printf("Listening on port %d", ps)
	for {
		conn, err := ln.Accept()
		if err != nil {
			log.Printf("Accept error on ps=%d: %v", ps, err)
			return
		}

		log.Printf("接受新连接 from %s on port %d", conn.RemoteAddr(), ps)

		wsConnsMux.Lock()
		sconn := wsConns[ps]
		wsConnsMux.Unlock()

		if sconn == nil {
			log.Printf("No WS for ps=%d", ps)
			conn.Close()
			continue
		}

		go proxyConn(conn, sconn, ps)
	}
}

// proxyConn 将 TCP <-> WS 数据双向转发，使用多路复用 ID
func proxyConn(tcpConn net.Conn, ws *SafeConn, ps int) {
	defer tcpConn.Close()
	// 生成唯一 ID
	id := fmt.Sprintf("%d-%d", time.Now().UnixNano(), tcpConn.RemoteAddr().(*net.TCPAddr).Port)

	log.Printf("新的TCP连接已建立: %s -> localhost:%d", tcpConn.RemoteAddr(), ps)

	// 建立响应通道
	respMu.Lock()
	ch := make(chan []byte, 100) // 使用缓冲通道
	respChans[id] = ch
	respMu.Unlock()

	// 通知客户端打开本地连接
	ws.WriteJSON(Message{ID: id, Type: "open"})

	// TCP -> WS
	go func() {
		buf := make([]byte, 4096)
		for {
			n, err := tcpConn.Read(buf)
			if n > 0 {
				ws.WriteJSON(Message{ID: id, Type: "data", Data: buf[:n]})
			}
			if err != nil {
				break
			}
		}
		ws.WriteJSON(Message{ID: id, Type: "close"})
	}()

	// WS -> TCP
	for data := range ch {
		tcpConn.Write(data)
	}

	// 清理
	respMu.Lock()
	delete(respChans, id)
	respMu.Unlock()
	log.Printf("Connection %s closed", id)
}

// handleWS 读取客户端通过 WS 回传的数据，并分发到对应通道
func handleWS(ws *SafeConn, ps int) {
	defer func() {
		ws.Close()

		wsConnsMux.Lock()
		delete(wsConns, ps)
		wsConnsMux.Unlock()

		log.Printf("WebSocket 连接已关闭 ps=%d", ps)
	}()

	for {
		var msg Message
		if err := ws.ReadJSON(&msg); err != nil {
			log.Printf("WS read error: %v", err)
			break
		}

		respMu.Lock()
		ch, ok := respChans[msg.ID]
		respMu.Unlock()

		if !ok {
			continue
		}

		if msg.Type == "data" {
			ch <- msg.Data
		} else if msg.Type == "close" {
			close(ch)
		}
	}
}

func (portInfo *PortMapping) FromRepo(obj *repo.IntranetTunnel) {
	portInfo.ClientName = obj.ClientName
	portInfo.CientUUID = obj.UUID
	portInfo.ClientIP = obj.ClientIp
	portInfo.Created = obj.CreateTime
	portInfo.PC = obj.ClientPort
	portInfo.PS = obj.ServerPort
}

func (portInfo *PortMapping) ToRepo() *repo.IntranetTunnel {
	return &repo.IntranetTunnel{
		UUID:               portInfo.CientUUID,
		Name:               portInfo.ClientName + ":" + strconv.Itoa(portInfo.PC),
		ClientName:         portInfo.ClientName,
		ClientIp:           portInfo.ClientIP,
		Protocol:           "tcp",
		ServerPort:         portInfo.PS,
		ClientPort:         portInfo.PC,
		Enable:             true,
		Encryption:         false,
		CreateTime:         portInfo.Created,
		LastConnectionTime: time.Now(),
	}
}

// 添加清理过期缓存的函数
func cleanExpiredCache(defaultCacheExpiration time.Duration) {
	for {
		time.Sleep(defaultCacheExpiration)

		now := time.Now()
		clientPortCacheMux.Lock()
		portMappingsMux.Lock()

		for cacheKey, ps := range clientPortCache {
			if mapping, exists := portMappings[ps]; exists {
				// 检查映射是否过期
				if now.Sub(mapping.Created) > portCacheExpiration {
					// 只从缓存中删除，不释放端口
					// 端口释放由handleListener负责
					delete(clientPortCache, cacheKey)
				}
			} else {
				// 如果映射不存在，从缓存中删除
				delete(clientPortCache, cacheKey)
			}
		}

		portMappingsMux.Unlock()
		clientPortCacheMux.Unlock()
	}
}

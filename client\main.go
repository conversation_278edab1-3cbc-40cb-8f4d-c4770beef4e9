package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"sync"
	"time"

	util "socks/client/util"

	"github.com/gorilla/websocket"
)

// Message 与 server 端一致
type Message struct {
	ID   string `json:"id"`
	Type string `json:"type"`
	Data []byte `json:"data,omitempty"`
}

// SafeConn 并发写保护
type SafeConn struct {
	*websocket.Conn
	wmu sync.Mutex
}

// Config 配置信息
type Config struct {
	HostName   string
	ServerIP   string
	ServerPort string
	LocalHost  string
	LocalPort  int
	APIPort    int
	UUID       string
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	conns map[string]net.Conn
	mu    sync.Mutex
}

// PortMapping 存储端口映射信息
type PortMapping struct {
	LocalPort  int                // 本地端口
	RemotePort int                // 远程端口
	WS         *SafeConn          // WebSocket连接
	CM         *ConnectionManager // 连接管理器
	Created    time.Time          // 创建时间
}

// 全局映射管理
var (
	// 存储所有活跃的端口映射
	portMappings    = make(map[int]*PortMapping) // 本地端口 -> 映射信息
	portMappingsMux sync.Mutex
)

func (c *SafeConn) WriteJSON(v interface{}) error {
	c.wmu.Lock()
	defer c.wmu.Unlock()
	return c.Conn.WriteJSON(v)
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		conns: make(map[string]net.Conn),
	}
}

// Add 添加连接
func (cm *ConnectionManager) Add(id string, conn net.Conn) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.conns[id] = conn
}

// Remove 移除连接
func (cm *ConnectionManager) Remove(id string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	if conn, ok := cm.conns[id]; ok {
		conn.Close()
		delete(cm.conns, id)
	}
}

// Get 获取连接
func (cm *ConnectionManager) Get(id string) net.Conn {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	return cm.conns[id]
}

// parseFlags 解析命令行参数
func parseFlags() (*Config, error) {
	config := &Config{
		HostName: util.GetHostName(),
		UUID:     util.GenerateUUID(),
	}
	flag.StringVar(&config.ServerIP, "server", "", "中转服务器IP")
	flag.StringVar(&config.ServerPort, "port", "", "中转服务器端口")
	flag.StringVar(&config.LocalHost, "host", "127.0.0.1", "本地服务IP")
	flag.IntVar(&config.LocalPort, "local", 0, "本地服务端口")
	flag.IntVar(&config.APIPort, "api", 8090, "API服务端口")
	flag.Parse()

	if config.ServerIP == "" || config.ServerPort == "" {
		return nil, fmt.Errorf("必需的参数未提供")
	}
	return config, nil
}

// allocatePort 向服务器申请端口
func allocatePort(config *Config, localPort int) (int, error) {
	// 获取本机IP地址
	localIP, err := util.GetLocalIP()
	if err != nil {
		log.Printf("获取本机IP失败: %v, 使用默认IP", err)
	}

	// 构建请求URL，包含本机IP
	url := fmt.Sprintf("http://%s:%s/allocate?pc=%d&client_ip=%s&id=%s&name=%s",
		config.ServerIP, config.ServerPort, localPort, localIP, config.UUID, config.HostName)

	resp, err := http.Get(url)
	if err != nil {
		return 0, fmt.Errorf("申请端口失败: %v", err)
	}
	defer resp.Body.Close()

	var alloc struct{ PS int }
	if err := json.NewDecoder(resp.Body).Decode(&alloc); err != nil {
		return 0, fmt.Errorf("解析响应失败: %v", err)
	}

	log.Printf("成功申请端口映射: 本地端口 %d -> 远程端口 %d (本机IP: %s)",
		localPort, alloc.PS, localIP)

	return alloc.PS, nil
}

// connectWebSocket 建立WebSocket连接
func connectWebSocket(config *Config, ps int) (*SafeConn, error) {
	wsURL := fmt.Sprintf("ws://%s:%s/ws?ps=%d", config.ServerIP, config.ServerPort, ps)
	wsRaw, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		return nil, fmt.Errorf("WebSocket连接失败: %v", err)
	}
	return &SafeConn{Conn: wsRaw}, nil
}

// handleLocalToWS 处理本地到WS的数据转发
func handleLocalToWS(id string, localConn net.Conn, ws *SafeConn) {
	buf := make([]byte, 4096)
	for {
		n, err := localConn.Read(buf)
		if n > 0 {
			ws.WriteJSON(Message{ID: id, Type: "data", Data: buf[:n]})
		}
		if err != nil {
			break
		}
	}
	ws.WriteJSON(Message{ID: id, Type: "close"})
}

// handleMessage 处理单个消息
func handleMessage(msg Message, ws *SafeConn, cm *ConnectionManager, localAddr string) {
	switch msg.Type {
	case "open":
		handleOpenMessage(msg, ws, cm, localAddr)
	case "data":
		handleDataMessage(msg, cm)
	case "close":
		cm.Remove(msg.ID)
	}
}

// handleOpenMessage 处理打开连接消息
func handleOpenMessage(msg Message, ws *SafeConn, cm *ConnectionManager, localAddr string) {
	localConn, err := net.Dial("tcp", localAddr)
	if err != nil {
		log.Printf("本地连接失败: %v", err)
		ws.WriteJSON(Message{ID: msg.ID, Type: "close"})
		return
	}
	cm.Add(msg.ID, localConn)
	go handleLocalToWS(msg.ID, localConn, ws)
}

// handleDataMessage 处理数据消息
func handleDataMessage(msg Message, cm *ConnectionManager) {
	if localConn := cm.Get(msg.ID); localConn != nil {
		localConn.Write(msg.Data)
	}
}

// processMessages 处理WebSocket消息循环
func processMessages(ws *SafeConn, cm *ConnectionManager, localAddr string) {
	for {
		var msg Message
		if err := ws.ReadJSON(&msg); err != nil {
			log.Printf("WS读取错误: %v", err)
			break
		}
		handleMessage(msg, ws, cm, localAddr)
	}
}

// setupPortMapping 设置新的端口映射
func setupPortMapping(config *Config, localPort int) (*PortMapping, error) {
	// 检查是否已存在映射
	portMappingsMux.Lock()
	if mapping, exists := portMappings[localPort]; exists {
		portMappingsMux.Unlock()
		return mapping, nil
	}
	portMappingsMux.Unlock()

	// 申请远程端口
	remotePort, err := allocatePort(config, localPort)
	if err != nil {
		return nil, fmt.Errorf("申请远程端口失败: %v", err)
	}
	log.Printf("为本地端口 %d 分配远程端口: %d", localPort, remotePort)

	// 建立WebSocket连接
	ws, err := connectWebSocket(config, remotePort)
	if err != nil {
		return nil, fmt.Errorf("建立WebSocket连接失败: %v", err)
	}
	log.Printf("本地端口 %d 的WebSocket已连接", localPort)

	// 创建连接管理器
	cm := NewConnectionManager()

	// 创建映射记录
	mapping := &PortMapping{
		LocalPort:  localPort,
		RemotePort: remotePort,
		WS:         ws,
		CM:         cm,
		Created:    time.Now(),
	}

	// 保存映射
	portMappingsMux.Lock()
	portMappings[localPort] = mapping
	portMappingsMux.Unlock()

	// 启动消息处理
	localAddr := fmt.Sprintf("%s:%d", config.LocalHost, localPort)
	go func() {
		processMessages(ws, cm, localAddr)
		// 当处理结束时，清理映射
		portMappingsMux.Lock()
		delete(portMappings, localPort)
		portMappingsMux.Unlock()
		log.Printf("本地端口 %d 的映射已关闭", localPort)
	}()

	return mapping, nil
}

// startAPIServer 启动API服务器
func startAPIServer(config *Config) {
	// 映射端点
	http.HandleFunc("/map", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost && r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 获取本地端口
		portStr := r.URL.Query().Get("port")
		if portStr == "" {
			http.Error(w, "Missing port parameter", http.StatusBadRequest)
			return
		}

		localPort := 0
		if _, err := fmt.Sscanf(portStr, "%d", &localPort); err != nil || localPort <= 0 || localPort > 65535 {
			http.Error(w, "Invalid port number", http.StatusBadRequest)
			return
		}

		// 设置映射
		mapping, err := setupPortMapping(config, localPort)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回映射信息
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"local_port":  mapping.LocalPort,
			"remote_port": mapping.RemotePort,
			"server":      config.ServerIP,
			"created":     mapping.Created,
		})
	})

	// 状态端点
	http.HandleFunc("/status", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		type MappingInfo struct {
			LocalPort  int       `json:"local_port"`
			RemotePort int       `json:"remote_port"`
			Created    time.Time `json:"created"`
		}

		var mappings []MappingInfo

		portMappingsMux.Lock()
		for _, mapping := range portMappings {
			mappings = append(mappings, MappingInfo{
				LocalPort:  mapping.LocalPort,
				RemotePort: mapping.RemotePort,
				Created:    mapping.Created,
			})
		}
		portMappingsMux.Unlock()

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"server":   fmt.Sprintf("%s:%s", config.ServerIP, config.ServerPort),
			"mappings": mappings,
			"count":    len(mappings),
		})
	})

	// 启动API服务器
	apiAddr := fmt.Sprintf("0.0.0.0:%d", config.APIPort)
	log.Printf("API服务器启动在 %s", apiAddr)
	if err := http.ListenAndServe(apiAddr, nil); err != nil {
		log.Fatalf("API服务器启动失败: %v", err)
	}
}

func main() {
	// 解析命令行参数
	config, err := parseFlags()
	if err != nil {
		flag.Usage()
		os.Exit(1)
	}

	// 如果指定了初始本地端口，则创建初始映射
	if config.LocalPort > 0 {
		localAddr := fmt.Sprintf("%s:%d", config.LocalHost, config.LocalPort)
		log.Printf("设置初始映射: %s", localAddr)

		_, err := setupPortMapping(config, config.LocalPort)
		if err != nil {
			log.Fatalf("设置初始映射失败: %v", err)
		}
	}

	// 启动API服务器
	startAPIServer(config)
}

package event

import (
	"fmt"
	"log"
	"net"
	"socks/server/domain/entity"
	"socks/server/domain/repo"
	"socks/server/infra/dao"
	"socks/server/util"
	"sync"
)

var (
	once              sync.Once
	connection        *Connection
	intranetTunnelDao = dao.GetIntranetTunnelDaoImpl()
)

type Connection struct {
	listeners *entity.ListenerGroup
	tunnels   *entity.TunnelGroup
	mapping   *entity.PortMappingGroup
}

func GetConnection(config *util.TunnelConfig) *Connection {
	once.Do(func() {
		connection = &Connection{
			listeners: entity.GetListenerGroup(),
			tunnels:   entity.GetTunnelGroup(),
			mapping:   entity.GetPortMappingGroup(config),
		}
	})
	return connection
}

func (c *Connection) AddMapping(uuid string, port int, mapping *entity.PortMapping) {
	c.mapping.AddMapping(uuid, port, mapping)

	// 持久化
	id, err := intranetTunnelDao.Create(repo.PortMapping2IntranetTunnel(mapping))
	if err != nil {
		log.Printf("save port mapping 2 pg fail, err: %v", err)
	}
	// 更新mapping的id，用以后续更新通道状态
	mapping.ID = id
}

func (c *Connection) GetMapping(uuid string, port int) *entity.PortMapping {
	return c.mapping.GetMapping(uuid, port)
}

func (c *Connection) DeleteMapping(uuid string, port int) {
	id := c.mapping.DeleteMapping(uuid, port)
	if id == entity.EMPTY_MAPPING_ID {
		return
	}

	err := intranetTunnelDao.Delete(id)
	if err != nil {
		log.Printf("delete port mapping from pg fail, err: %v", err)
	}
}

func (c *Connection) UpdateOnlineStatus(uuid string, port int, online bool) error {
	// 更新缓存对象
	c.mapping.UpdateOnlineStatus(uuid, port, online)
	// 更新数据库对象
	mapping := c.mapping.GetMapping(uuid, port)
	return intranetTunnelDao.Update(repo.PortMapping2IntranetTunnel(mapping))
}

func (c *Connection) IsServerPortAllocated(serverPort int) bool {
	return c.mapping.GetClientUUIDbyServerPort(serverPort) != ""

}

func (c *Connection) AddTunnel(uuid string, conn net.Conn) {
	c.tunnels.AddTunnel(uuid, entity.NewSafeConn(conn))
}

func (c *Connection) GetTunnel(uuid string) *entity.SafeConn {
	return c.tunnels.GetTunnel(uuid)
}

func (c *Connection) GetTunnelByServerPort(serverPort int) *entity.SafeConn {
	cilentUUID := c.mapping.GetClientUUIDbyServerPort(serverPort)
	if cilentUUID == "" {
		log.Printf("not find server-client conn")
		return nil
	}
	return c.GetTunnel(cilentUUID)
}

func (c *Connection) ActivateTunnel(uuid string) {
	tunnel := c.tunnels.GetTunnel(uuid)
	if tunnel == nil {
		log.Printf("tunnel is nil, client uuid: %s", uuid)
		return
	}
	for {
		var msg entity.ConnMessage
		if err := tunnel.ReadJSON(&msg); err != nil {
			log.Printf("TCP read error: %v", err)
			break
		}
		switch msg.Type {
		case "ping":
			response := entity.ConnMessage{
				ID:   msg.ID,
				Type: "pong",
				Data: []byte("pong"),
			}
			if err := tunnel.WriteJSON(response); err != nil {
				log.Printf("发送pong响应失败: %v", err)
				// 增加连接重试机制
				return
			}
		case "data":
			if ch := tunnel.GetResponseChan(msg.ID); ch != nil {
				select {
				case ch <- msg.Data: // 非阻塞发送
				default:
					log.Printf("警告: 通道已满，消息可能丢失: id=%s", msg.ID)
				}
			}
		case "close":
			tunnel.DeleteRespChan(msg.ID)
		default:
			log.Printf("未知消息类型: %s", msg.Type)
		}
	}
}

func (c *Connection) CloseTunnel(uuid string) {
	c.tunnels.CloseTunnel(uuid)
	c.mapping.UpdateOnlineStatusByClientStatus(uuid, false)
}

func (c *Connection) AllocatePort() (int, error) {
	listener := &entity.Listener{}
	for port := c.mapping.MinPort; port <= c.mapping.MaxPort; port++ {
		if c.IsServerPortAllocated(port) {
			continue
		}
		err := listener.BuildListener("tcp", "0.0.0.0", port)
		if err != nil {
			continue
		}

		c.listeners.AddListener(port, listener)

		log.Printf("成功分配端口: %d", port)
		return port, nil
	}

	return 0, fmt.Errorf("无法分配可用端口，所有端口已被占用")
}

func (c *Connection) GetListener(serverPort int) *entity.Listener {
	return c.listeners.GetListener(serverPort)
}

func (c *Connection) CloseListener(serverPort int) {
	c.listeners.DeleteListener(serverPort)
}

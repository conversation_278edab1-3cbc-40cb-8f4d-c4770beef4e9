package util

import (
	"fmt"
	"log"
	"net"
	"os"

	"github.com/google/uuid"
)

func GetHostName() string {
	hostname, err := os.Hostname()
	if err != nil {
		log.Println("无法获取主机名:", err)
		return ""
	}

	return hostname
}

func GetLocalIP() (string, error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "127.0.0.1", err
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String(), nil
			}
		}
	}

	return "127.0.0.1", nil
}

func GenerateUUID() string {
	macAddr, err := getMACAddress()
	if err != nil {
		log.Println(err)
		return ""
	}

	ipAddr, err := GetLocalIP()
	if err != nil {
		log.Println(err)
		return ""
	}

	// 使用 MAC 地址和 IP 地址的哈希生成 UUID
	namespaceUUID := uuid.NewMD5(uuid.Nil, []byte(macAddr+ipAddr))

	return namespaceUUID.String()
}

func getMACAddress() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, inter := range interfaces {
		if len(inter.HardwareAddr) > 0 {
			return inter.HardwareAddr.String(), nil
		}
	}
	return "", fmt.Errorf("no MAC address found")
}

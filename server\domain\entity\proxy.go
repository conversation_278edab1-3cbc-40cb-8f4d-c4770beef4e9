package entity

import (
	"fmt"
	"net"
	"socks/server/util"
	"strings"
	"sync"
	"time"
)

const (
	EMPTY_MAPPING_ID = -1
)

var (
	portmappingGroup *PortMappingGroup
	pmgOnce          sync.Once
	listenerGroup    *ListenerGroup
	lgOnce           sync.Once
)

type Protocol struct {
	TCP bool
	UDP bool
}

type Listener struct {
	ProxyListener net.Listener
}

type ListenerGroup struct {
	Listeners map[int]*Listener
	Locker    sync.RWMutex
}

// PortMapping 存储端口映射信息
type PortMapping struct {
	ID          int    // 数据库记录id
	Name        string // 隧道名称 主机名称:代理端口
	Client      *Client
	ClientPort  int       // 本地端口
	ServerPort  int       // 公网端口
	Listener    *Listener // 监听器
	Created     time.Time // 创建时间
	Enable      bool      // 代理是否启用
	Online      bool      // 是否在线
	Encryption  bool      // 是否加密
	Password    string    // 认证密钥
	RateLimit   int       // 最大连接数
	ServiceName string    // 具体端口对应的服务名称，不可为空
	Description string    // 代理描述
	Protocol    *Protocol // 当前端口映射的协议
}

type PortMappingGroup struct {
	MinPort           int
	MaxPort           int
	Timeout           int
	MaxConnection     int
	SlidingExpiration int
	Mappings          map[string]*PortMapping // 客户端uuid+端口映射一张PortMapping表，实现持久化
	ServerPortMapping map[int]string          //代理服务端端口和客户端uuid的映射关系，方便快速查询
	Locker            sync.RWMutex
}

func (p *Protocol) GetProtocolType() string {
	if p.TCP && p.UDP {
		return "TCP,UDP"
	} else if p.TCP {
		return "TCP"
	} else if p.UDP {
		return "UDP"
	} else {
		return "未知"
	}
}

func BuildProtocol(protocolType string) *Protocol {
	p := &Protocol{}
	if strings.Contains(protocolType, "TCP") {
		p.TCP = true
	}

	if strings.Contains(protocolType, "UDP") {
		p.UDP = true
	}

	return p
}

func GetListenerGroup() *ListenerGroup {
	lgOnce.Do(func() {
		listenerGroup = &ListenerGroup{
			Listeners: make(map[int]*Listener),
		}
	})
	return listenerGroup
}

func GetPortMappingGroup(config *util.TunnelConfig) *PortMappingGroup {
	pmgOnce.Do(func() {
		portmappingGroup = &PortMappingGroup{
			MinPort:           config.MinPort,
			MaxPort:           config.MaxPort,
			Timeout:           config.Timeout,
			MaxConnection:     config.MaxConnection,
			SlidingExpiration: config.SlidingExpiration,
			Mappings:          make(map[string]*PortMapping),
			ServerPortMapping: make(map[int]string),
		}
	})

	return portmappingGroup
}

func (l *Listener) BuildListener(netType string, ip string, port int) error {

	address := fmt.Sprintf("%s:%d", ip, port)

	switch netType {
	case "tcp", "tcp4", "tcp6":
		ln, err := net.Listen(netType, address)
		if err != nil {
			return fmt.Errorf("创建 %s 监听器失败: %v", netType, err)
		}
		l.ProxyListener = ln

	case "udp", "udp4", "udp6":
		// UDP 不使用 net.Listener，而是使用 net.PacketConn
		// 这里我们返回错误，因为 Listener 结构体当前设计用于 TCP
		return fmt.Errorf("UDP 协议不支持 net.Listener 接口，请使用 PacketConn")

	default:
		return fmt.Errorf("不支持的网络类型: %s", netType)
	}

	return nil
}

func (l Listener) GetListener() net.Listener {
	return l.ProxyListener
}

func (g *ListenerGroup) AddListener(serverPort int, listener *Listener) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	g.Listeners[serverPort] = listener
}

func (g *ListenerGroup) GetListener(serverPort int) *Listener {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if listener, ok := g.Listeners[serverPort]; ok {
		return listener
	}
	return nil
}

func (g *ListenerGroup) DeleteListener(serverPort int) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	if listener, ok := g.Listeners[serverPort]; ok {
		listener.ProxyListener.Close()
	}
	delete(g.Listeners, serverPort)
}

func (g *PortMappingGroup) AddMapping(clientUUID string, clientPort int, mapping *PortMapping) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	g.Mappings[cacheKey] = mapping
	g.ServerPortMapping[mapping.ServerPort] = clientUUID
}

func (g *PortMappingGroup) GetMapping(clientUUID string, clientPort int) *PortMapping {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	if mapping, ok := g.Mappings[cacheKey]; ok {
		return mapping
	}
	return nil
}

func (g *PortMappingGroup) DeleteMapping(clientUUID string, clientPort int) int {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	mapping := g.GetMapping(clientUUID, clientPort)
	if mapping == nil {
		return EMPTY_MAPPING_ID
	}
	// 必须删除ServerPortMapping这个map，通过它来判断端口是否已经分配
	delete(g.ServerPortMapping, mapping.ServerPort)
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	delete(g.Mappings, cacheKey)
	return mapping.ID
}

func (g *PortMappingGroup) GetClientUUIDbyServerPort(serverPort int) string {
	return g.ServerPortMapping[serverPort]
}

func (g *PortMappingGroup) UpdateOnlineStatus(clientUUID string, clientPort int, online bool) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	if mapping, ok := g.Mappings[cacheKey]; ok {
		mapping.Online = online
	}
}

func (g *PortMappingGroup) UpdateOnlineStatusByClientStatus(uuid string, online bool) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	for _, mapping := range g.Mappings {
		if mapping.Client.UUID == uuid {
			mapping.Online = online
		}
	}
}

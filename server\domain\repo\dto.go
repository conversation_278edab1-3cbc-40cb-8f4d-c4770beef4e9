package repo

import (
	"socks/server/domain/entity"
	"socks/server/infra/repo"
	"time"
)

func PortMapping2IntranetTunnel(mapping *entity.PortMapping) *repo.IntranetTunnel {
	return &repo.IntranetTunnel{
		ID:                 mapping.ID,
		UUID:               mapping.Client.UUID,
		Name:               mapping.Name,
		ClientName:         mapping.Client.Name,
		ClientIp:           mapping.Client.IP,
		Protocol:           mapping.Protocol.GetProtocolType(),
		ServerPort:         mapping.ServerPort,
		ClientPort:         mapping.ClientPort,
		Enable:             mapping.Enable,
		Description:        mapping.Description,
		Encryption:         mapping.Encryption,
		Password:           mapping.Password,
		RateLimit:          mapping.RateLimit,
		ClientType:         mapping.Client.Type,
		CreateTime:         mapping.Created,
		LastConnectionTime: time.Now(),
		Online:             mapping.Online,
		ServiceName:        mapping.ServiceName,
	}
}

func IntranetTunnel2PortMapping(it *repo.IntranetTunnel) *entity.PortMapping {
	return &entity.PortMapping{
		ID:   it.ID,
		Name: it.Name,
		Client: &entity.Client{
			UUID: it.UUID,
			Name: it.ClientName,
			IP:   it.ClientIp,
			Type: it.ClientType,
		},
		ClientPort:  it.ClientPort,
		ServerPort:  it.ServerPort,
		Created:     it.CreateTime,
		Enable:      it.Enable,
		Online:      it.Online,
		Description: it.Description,
		Encryption:  it.Encryption,
		Password:    it.Password,
		RateLimit:   it.RateLimit,
		ServiceName: it.ServiceName,
		Protocol:    entity.BuildProtocol(it.Protocol),
	}
}

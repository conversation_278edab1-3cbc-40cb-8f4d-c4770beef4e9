package service

import (
	"fmt"
	"log"
	"net"
	"socks/server/domain/entity"
	"socks/server/domain/event"
	"socks/server/util"
	"sync"
	"time"
)

var (
	proxyService *ProxyService
	psOnce       sync.Once
)

type ProxyService struct {
	clients    *event.RegisterClient
	connection *event.Connection
	tunnels    map[int]*event.ProxyTunnel
}

func GetProxyService(config *util.TunnelConfig) *ProxyService {
	psOnce.Do(func() {
		proxyService = &ProxyService{
			clients:    event.GetRegisterClient(),
			connection: event.GetConnection(config),
			tunnels:    make(map[int]*event.ProxyTunnel),
		}
	})
	return proxyService
}

// 底层通讯层面的代理通讯
func (p *ProxyService) RegisterClient(client *entity.Client, conn net.Conn) error {
	err := p.clients.AddClient(client)
	if err != nil {
		return err
	}
	// 增加通道
	p.connection.AddTunnel(client.UUID, conn)
	// 激活通道，可以增加一个 ch 用来关闭通道
	go p.connection.ActivateTunnel(client.UUID)
	return nil
}

// 端口层面的代理通讯-锁定一个端口，更新映射表
func (p *ProxyService) AllocateAvailablePort(clientUUID string, clientPort int, serviceName string) (int, error) {

	// 先查看映射表中是否存在映射
	mapping := p.connection.GetMapping(clientUUID, clientPort)
	if mapping != nil {
		return mapping.ServerPort, nil
	}

	// 检查客户端是否注册
	client := p.clients.GetClient(clientUUID)
	if client == nil {
		return 0, fmt.Errorf("client not register")
	}

	serverPort, err := p.connection.AllocatePort()
	if err != nil {
		return serverPort, err
	}

	p.connection.AddMapping(clientUUID, clientPort, &entity.PortMapping{
		Name:        client.GetTunnelName(clientPort),
		Client:      client,
		ClientPort:  clientPort,
		ServerPort:  serverPort,
		Listener:    p.connection.GetListener(serverPort),
		Created:     time.Now(),
		Enable:      true,
		Online:      true,
		Encryption:  false,
		Password:    "",
		RateLimit:   0,
		ServiceName: serviceName,
		Description: "",
		Protocol: &entity.Protocol{
			TCP: true,
			UDP: false,
		},
	})

	return serverPort, nil
}

// 端口层面的代理通讯，将访问serverPort的请求通过tunnel转发至目标服务
// request -> server port -> listener(proxy) -> tunnel(chan-server) -> tunnel -> client port -> listener(client) ↓
// request <- server port <- listener(proxy) <- tunnel(chan-server) <- tunnel <- tunnel(chan-client) <------------
func (p *ProxyService) StartProxy(clientUUID string, clientPort int, serverPort int) error {
	if _, ok := p.tunnels[serverPort]; ok {
		return fmt.Errorf("already proxy in server port: %d", serverPort)
	}
	tunnel := event.NewProxyTunnel(p.connection, serverPort, clientPort, clientUUID)
	p.tunnels[serverPort] = tunnel
	go tunnel.StartProxy()
	return nil
}

func (p *ProxyService) StopProxy(serverPort int) error {
	tunnel, ok := p.tunnels[serverPort]
	if !ok {
		log.Printf("proxy already removed, server port: %d", serverPort)
		return nil
	}
	return tunnel.StopProxy()
}

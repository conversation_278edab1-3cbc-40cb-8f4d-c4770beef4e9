package handlers

import (
	"encoding/json"
	"log"
	"net"
	"net/http"
	"socks/server/application/service"
	"socks/server/server/dto"
	"socks/server/util"
	"strconv"
)

type PortProxyHandler struct {
	portProxyService service.PortProxyService
}

func GetPortProxyHandler() *PortProxyHandler {
	return &PortProxyHandler{
		portProxyService: service.GetPortProxyService(util.SystemConfig),
	}
}

// 显示当前端口映射状态
// func statusHandler(w http.ResponseWriter, r *http.Request) {
// 	if r.Method != http.MethodGet {
// 		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
// 		return
// 	}

// 	w.Header().Set("Content-Type", "application/json")
// 	json.NewEncoder(w).Encode(map[string]interface{}{
// 		"mappings": ,
// 		"count":    ,
// 	})
// }

// 分配一个公网端口，并为该 pc 启动监听
func (p *PortProxyHandler) AllocateHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取参数
	clientUUID := r.URL.Query().Get("id")
	if clientUUID == "" {
		http.Error(w, "invalid id", http.StatusBadRequest)
		return
	}
	portStr := r.URL.Query().Get("port")
	if portStr == "" {
		http.Error(w, "missing port", http.StatusBadRequest)
		return
	}
	port, err := strconv.Atoi(portStr)
	if err != nil {
		http.Error(w, "invalid port value", http.StatusBadRequest)
		return
	}
	serviceName := r.URL.Query().Get("service_name")

	servicePort, err := p.portProxyService.AllocateServerPort(clientUUID, port, serviceName)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	p.portProxyService.Start(clientUUID, port, servicePort)

	log.Printf("分配端口 ps=%d 给 pc=%d (客户端UUID: %s)", servicePort, port, clientUUID)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"port": servicePort,
	})
}

// connectionHandler 处理控制连接请求
func (p *PortProxyHandler) RegisterHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求，后续会升级为TCP连接
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	clientName := r.URL.Query().Get("name")
	clientType := r.URL.Query().Get("type")
	clientUUID := r.URL.Query().Get("id")
	if clientUUID == "" {
		http.Error(w, "invalid id", http.StatusBadRequest)
		return
	}
	clientIP := r.URL.Query().Get("ip")
	if clientIP == "" {
		// 如果客户端没有上报IP，使用备用方法
		clientIP = r.RemoteAddr
		// 如果有代理，尝试获取真实IP
		if forwardedFor := r.Header.Get("X-Forwarded-For"); forwardedFor != "" {
			clientIP = forwardedFor
		}
		// 去除端口部分
		if host, _, err := net.SplitHostPort(clientIP); err == nil {
			clientIP = host
		}
	}

	client := dto.NewClient(clientUUID, clientIP, clientName, clientType)

	// 升级HTTP连接为TCP连接
	hj, ok := w.(http.Hijacker)
	if !ok {
		http.Error(w, "Hijacking not supported", http.StatusInternalServerError)
		return
	}

	conn, bufrw, err := hj.Hijack()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 发送HTTP 101状态码表示切换协议
	response := "HTTP/1.1 101 Switching Protocols\r\n" +
		"Upgrade: tcp\r\n" +
		"Connection: Upgrade\r\n\r\n"

	if _, err := bufrw.WriteString(response); err != nil {
		conn.Close()
		log.Printf("写入升级响应失败: %v", err)
		return
	}

	if err := bufrw.Flush(); err != nil {
		conn.Close()
		log.Printf("刷新缓冲区失败: %v", err)
		return
	}

	log.Printf("连接已升级为TCP, client id: %s", clientUUID)

	err = p.portProxyService.RegisterClient(client, conn)
	if err != nil {
		log.Print(err)
		return
	}

	// 立即发送一个确认消息给客户端，确保连接正常
	response2 := map[string]interface{}{
		"id":   "register_confirm",
		"type": "register_success",
		"data": []byte("registration successful"),
	}

	responseData, err2 := json.Marshal(response2)
	if err2 == nil {
		conn.Write(responseData)
		conn.Write([]byte("\n")) // 添加换行符作为消息分隔符
		log.Printf("已发送注册确认消息给客户端: %s", clientUUID)
	}

}
